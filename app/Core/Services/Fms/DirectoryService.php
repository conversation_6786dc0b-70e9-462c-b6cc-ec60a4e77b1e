<?php

declare(strict_types=1);

namespace App\Core\Services\Fms;

use App\Core\Services\BusinessService;
use App\Model\TchipBiFms\FmsDirectoryModel;
use App\Model\TchipBiFms\FmsFileModel;
use App\Model\TchipBiFms\FmsRecycleBinModel;
use App\Exception\AppException;
use App\Constants\StatusCode;
use Carbon\Carbon;
use Hyperf\DbConnection\Db;
use Qbhy\HyperfAuth\AuthManager;

/**
 * FMS目录服务
 */
class DirectoryService extends BusinessService
{
    protected $model = FmsDirectoryModel::class;

    /**
     * 获取目录列表
     */
    public function getDirectoryList(?int $parentId = null, string $search = '', int $page = 1, int $pageSize = 20, int $union = 0): array
    {
        // 当 union = 1 时，启用联合查询功能
        if ($union === 1) {
            return $this->getUnionDirectoryFileList($parentId, $search, $page, $pageSize);
        }

        $query = FmsDirectoryModel::query()
            ->with([ 'children'])
            ->where('is_deleted', 0)
            ->orderBy('sort_order', 'asc')
            ->orderBy('created_at', 'desc');

        // 按父目录筛选
        if ($parentId !== null) {
            $query->where('parent_id', $parentId);
        } else {
            $query->whereNull('parent_id');
        }

        // 搜索
        if (!empty($search)) {
            $query->where('name', 'like', '%' . $search . '%');
        }

        // 分页
        $total = $query->count();
        $directories = $query->offset(($page - 1) * $pageSize)
            ->limit($pageSize)
            ->get()
            ->toArray();

        return [
            'data' => $directories,
            'total' => $total,
            'page' => $page,
            'page_size' => $pageSize,
        ];
    }

    /**
     * 创建目录
     */
    public function createDirectory(array $data)
    {
        // 验证父目录是否存在
        if (!empty($data['parent_id'])) {
            $parent = FmsDirectoryModel::find($data['parent_id']);
            if (!$parent) {
                throw new AppException(StatusCode::ERR_SERVER, '父目录不存在');
            }


        }


        $data['owner_id']   = $data['owner_id']   ?? make(\Qbhy\HyperfAuth\AuthManager::class)->user()->getId();
        $data['owner_type'] = $data['owner_type'] ?? 'user';           // 默认类型
        $data['path']       = '';  // 先给空，保存后再调用 updatePath() 来生成

        $directory = FmsDirectoryModel::create($data);
        // 更新路径
        $directory->updatePath();

        // 记录操作日志
        $this->logOperation('create', 'directory', $directory->id, $directory->name);

        return $directory;
    }

    /**
     * 更新目录
     */
    public function updateDirectory(int $id, array $data): FmsDirectoryModel
    {
        $directory = FmsDirectoryModel::findOrFail($id);


        $data['updated_by'] = $this->getCurrentUserId();
        $data['updated_at'] = Carbon::now();

        $directory->update($data);

        // 如果修改了名称，更新路径
        if (isset($data['name'])) {
            $directory->updatePath();
        }

        // 记录操作日志
        $this->logOperation('update', 'directory', $directory->id, $directory->name);

        return $directory;
    }

    /**
     * 删除目录（软删除）
     */
    public function deleteDirectory(int $id): bool
    {
        $directory = FmsDirectoryModel::findOrFail($id);


        // 检查是否有子目录或文件
        if ($directory->hasChildren() || $directory->files()->exists()) {
            throw new AppException(StatusCode::ERR_SERVER, '目录不为空，无法删除');
        }

        return Db::transaction(function () use ($directory) {
            // 记录到回收站
            FmsRecycleBinModel::create([
                'target_type' => 'directory',
                'target_id' => $directory->id,
                'target_name' => $directory->name,
                'target_path' => $directory->full_path,
                'target_data' => $directory->toArray(),
                'deleted_by' => $this->getCurrentUserId(),
                'deleted_at' => Carbon::now(),
            ]);

            // 软删除
            $directory->deleted_by = $this->getCurrentUserId();
            $directory->save();
            $directory->delete();

            // 记录操作日志
            $this->logOperation('delete', 'directory', $directory->id, $directory->name);

            return true;
        });
    }

    /**
     * 移动目录
     */
    public function moveDirectory(int $id, ?int $newParentId): FmsDirectoryModel
    {
        $directory = FmsDirectoryModel::findOrFail($id);

        // 验证新父目录
        if ($newParentId) {
            $newParent = FmsDirectoryModel::findOrFail($newParentId);

            // 检查是否移动到自己的子目录
            if ($directory->isChildOf($newParent) || $directory->id === $newParent->id) {
                throw new AppException(StatusCode::ERR_SERVER, '不能移动到自己的子目录');
            }
        }

        // 检查新位置是否有同名目录
        $exists = FmsDirectoryModel::where('parent_id', $newParentId)
                              ->where('name', $directory->name)
                              ->where('id', '!=', $id)
                              ->exists();
        if ($exists) {
            throw new AppException(StatusCode::ERR_SERVER, '目标位置已存在同名目录');
        }

        $directory->parent_id = $newParentId;
        $directory->updated_by = $this->getCurrentUserId();
        $directory->save();

        // 更新路径
        $directory->updatePath();

        // 记录操作日志
        $this->logOperation('move', 'directory', $directory->id, $directory->name);

        return $directory;
    }

    /**
     * 获取目录树
     */
    public function getDirectoryTree(?int $parentId = null, int $maxDepth = 5): array
    {
        $query = FmsDirectoryModel::with(['children' => function ($query) use ($maxDepth) {
            if ($maxDepth > 1) {
                $query->with('allChildren');
            }
        }]);

        if ($parentId) {
            $query->where('parent_id', $parentId);
        } else {
            $query->whereNull('parent_id');
        }

        $directories = $query->orderBy('sort_order')->orderBy('name')->get();

        return $this->buildTree($directories, $maxDepth);
    }

    /**
     * 构建目录树结构
     */
    private function buildTree($directories, int $maxDepth, int $currentDepth = 1): array
    {
        $tree = [];

        foreach ($directories as $directory) {
            $node = [
                'id' => $directory->id,
                'name' => $directory->name,
                'path' => $directory->full_path,
                'level' => $directory->getLevel(),
                'has_children' => $directory->hasChildren(),
                'isLeaf' => !$directory->hasChildren(), // antd a-tree只认 isLeaf
                'children' => []
            ];

            if ($currentDepth < $maxDepth && $directory->children->isNotEmpty()) {
                $node['children'] = $this->buildTree($directory->children, $maxDepth, $currentDepth + 1);
            }

            $tree[] = $node;
        }

        return $tree;
    }

    /**
     * 获取目录内容（子目录和文件）
     */
    public function getDirectoryContents(int $directoryId, array $filters = []): array
    {
        $directory = FmsDirectoryModel::findOrFail($directoryId);



        // 获取子目录
        $subdirectories = FmsDirectoryModel::where('parent_id', $directoryId)
                                     ->orderBy('sort_order')
                                     ->orderBy('name')
                                     ->get();

        // 获取文件
        $filesQuery = FmsFileModel::where('directory_id', $directoryId);

        // 应用过滤条件
        if (!empty($filters['name'])) {
            $filesQuery->where('name', 'like', '%' . $filters['name'] . '%');
        }

        if (!empty($filters['mime_type'])) {
            $filesQuery->where('mime_type', 'like', $filters['mime_type'] . '%');
        }

        $files = $filesQuery->orderBy('name')->get();

        return [
            'directory' => $directory,
            'subdirectories' => $subdirectories,
            'files' => $files,
            'total_subdirectories' => $subdirectories->count(),
            'total_files' => $files->count(),
        ];
    }



    /**
     * 记录操作日志（临时实现，后续会被日志服务替代）
     */
    private function logOperation(string $action, string $targetType, int $targetId, string $targetName): void
    {
        // TODO: 实现操作日志记录
        // 这里暂时为空，等日志服务实现后再替换
    }

    /**
     * 获取当前用户ID（临时实现）
     */
    private function getCurrentUserId(): int
    {
        // TODO: 从认证上下文获取当前用户ID
        return 1; // 临时返回1
    }

    /**
     * 联合查询目录和文件列表
     */
    private function getUnionDirectoryFileList(?int $parentId = null, string $search = '', int $page = 1, int $pageSize = 20): array
    {
        // 构建目录查询
        $directoryQuery = FmsDirectoryModel::query()->getQuery()
            ->select([
                'id',
                'name',
                'created_at',
                'updated_at',
                'sort_order',
                Db::raw("'directory' as item_type"),
                Db::raw("0 as item_type_order"), // 目录排序优先级为0
                'parent_id',
                Db::raw("NULL as directory_id"),
                Db::raw("NULL as size"),
                Db::raw("NULL as mime_type"),
                Db::raw("NULL as version"),
                'visibility',
                Db::raw("NULL as created_by"),
                Db::raw("NULL as updated_by")
            ])
            ->where('is_deleted', 0);

        // 构建文件查询
        $fileQuery = FmsFileModel::query()->getQuery()
            ->select([
                'id',
                'name',
                'created_at',
                'updated_at',
                'sort_order',
                Db::raw("'file' as item_type"),
                Db::raw("1 as item_type_order"), // 文件排序优先级为1
                Db::raw("NULL as parent_id"),
                'directory_id',
                'size',
                'mime_type',
                'version',
                'visibility',
                'created_by',
                'updated_by'
            ])
            ->where('is_deleted', 0);

        // 应用父目录筛选条件
        if ($parentId !== null) {
            $directoryQuery->where('parent_id', $parentId);
            $fileQuery->where('directory_id', $parentId);
        } else {
            $directoryQuery->whereNull('parent_id');
            // 对于文件，当 parentId 为 null 时，查询根目录下的文件（directory_id 为 null 或 0）
            $fileQuery->where(function ($query) {
                $query->whereNull('directory_id')->orWhere('directory_id', 0);
            });
        }

        // 应用搜索条件
        if (!empty($search)) {
            $directoryQuery->where('name', 'like', '%' . $search . '%');
            $fileQuery->where('name', 'like', '%' . $search . '%');
        }

        // 执行联合查询
        $unionQuery = $directoryQuery->unionAll($fileQuery);

        // 计算总数（需要单独查询）
        $totalDirectories = clone $directoryQuery;
        $totalFiles = clone $fileQuery;
        $directoryCount = $totalDirectories->count();
        $fileCount = $totalFiles->count();
        $total = $directoryCount + $fileCount;

        // 应用排序：先按类型排序（目录优先），再按 sort_order，最后按创建时间
        $results = Db::table('') // 占位，马上被fromSub执行替换
            ->fromSub($unionQuery, 'union_results')
            ->orderBy('item_type_order', 'asc')
            ->orderBy('sort_order', 'asc')
            ->orderBy('created_at', 'desc')
            ->offset(($page - 1) * $pageSize)
            ->limit($pageSize)
            ->get()
            ->toArray();

        // $results = Db::table(Db::raw("({$unionQuery->toSql()}) as union_results"))
        //     ->mergeBindings($unionQuery)
        //     ->orderBy('item_type_order', 'asc')  // 目录优先
        //     ->orderBy('sort_order', 'asc')       // 排序字段
        //     ->orderBy('created_at', 'desc')      // 创建时间
        //     ->offset(($page - 1) * $pageSize)
        //     ->limit($pageSize)
        //     ->get()
        //     ->toArray();

        // 转换结果为数组格式
        $data = array_map(function ($item) {
            return (array) $item;
        }, $results);

        return [
            'data' => $data,
            'total' => $total,
            'directory_count' => $directoryCount,
            'file_count' => $fileCount,
            'page' => $page,
            'page_size' => $pageSize,
        ];
    }
}