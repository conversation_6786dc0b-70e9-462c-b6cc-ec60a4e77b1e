<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateUserNoticePushTable extends Migration
{
    /**
     * 消息推送表
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('user_notice_push')) {
            Schema::create('user_notice_push', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->integer('user_id')->comment('uid');
                $table->char('notice_type', 16)->comment('消息类型');
                $table->text('content')->comment('消息内容');
                $table->string('notice_mode', 16)->comment('已推送的渠道');
                $table->tinyInteger('status')->default(0)->comment('消息状态,是否已推送等');
                $table->tinyInteger('is_read')->default(0)->comment('是否已阅读');
                $table->dateTime('created_at');
                $table->dateTime('updated_at');
                $table->dateTime('deleted_at')->nullable();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_notice_push');
    }
}
