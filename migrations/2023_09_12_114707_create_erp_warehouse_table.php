<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateErpWarehouseTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('erp_warehouse')) {
            Schema::create('erp_warehouse', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->char('code', 8)->comment('仓位编码');
                $table->string('name', 32)->comment('名称');
                $table->tinyInteger('see')->default(1)->comment('是否可显示数据');
                $table->tinyInteger('borrow')->default(0)->comment('是否可借用');
                $table->tinyInteger('status')->default(1)->comment('状态');
                $table->dateTime('created_at');
                $table->dateTime('updated_at');
                $table->dateTime('deleted_at')->nullable();
            });
        }

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('erp_warehouse');
    }
}
