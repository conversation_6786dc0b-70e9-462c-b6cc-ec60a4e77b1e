<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateReleaseVersionTable extends Migration
{
    protected $connection = 'tchip_redmine';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('release_version')) {
            Schema::create('release_version', function (Blueprint $table) {
                $table->increments('id');
                $table->integer('prev_version_id')->default(0)->comment('上一个版本');
                $table->integer('project_id')->comment('项目ID');
                $table->string('name', '64')->comment('版本名称');
                $table->longText('description')->nullable()->comment('描述');
                $table->dateTime('start_date')->nullable()->comment('开始日期');
                $table->dateTime('effective_date')->nullable()->comment('截止日期');
                $table->char('status', 16)->default('open')->comment('状态');
                $table->char('sharing', 16)->nullable()->comment('关系');
                $table->integer('responsible_user_id')->comment('负责人');
                $table->dateTime('created_at');
                $table->dateTime('updated_at');
                $table->dateTime('deleted_at')->nullable();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('release_version');
    }
}
