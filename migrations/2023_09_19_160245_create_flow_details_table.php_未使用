<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateFlowDetailsTable extends Migration
{
    protected $connection = 'tchip_redmine';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('flow_details')) {
            Schema::create('flow_details', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->bigInteger('pid')->comment('上级流程ID');
                $table->bigInteger('flow_id')->comment('流程主ID');
                $table->string('name', 32)->comment();
                $table->tinyInteger('status')->comment('节点当前状态。3:未开始、2:进行中、1:已完成');
                $table->bigInteger('next_id', 32)->comment('下一个流程ID');
                $table->bigInteger('handle_uid', 32)->comment('处理人ID');
                $table->smallInteger('issue_status')->comment('本流程对应的事项状态');
                $table->dateTime('start_date')->comment('开始日期');
                $table->dateTime('complete_date')->comment('完成日期');
                $table->dateTime('created_at');
                $table->dateTime('updated_at');
                $table->dateTime('deleted_at')->nullable();
            });
        }

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('flow_details');
    }
}
