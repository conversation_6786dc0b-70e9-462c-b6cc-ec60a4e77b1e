<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateProjectsDescTable extends Migration
{
    protected $connection = 'tchip_redmine';
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('projects_desc')) {
            Schema::create('projects_desc', function (Blueprint $table) {
                // 执行后需要手动去表中加入自动递增
                $table->bigIncrements('id');
                $table->integer('project_id')->comment('项目ID');
                $table->smallInteger('desc_id')->comment('描述ID');
                $table->smallInteger('value')->comment('描述值');
                $table->string('url', 512)->comment('地址');
                $table->dateTime('created_at');
                $table->dateTime('updated_at');
                // $table->primary(['project_id', 'desc_id']);
            });
        }

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('project_desc');
    }
}
