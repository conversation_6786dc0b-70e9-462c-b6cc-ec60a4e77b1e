<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateProjectsAttrTable extends Migration
{
    protected $connection = 'tchip_redmine';
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('projects_attr', function (Blueprint $table) {
            // 执行后需要手动去表中加入自动递增
            $table->bigIncrements('id');
            $table->integer('project_id')->comment('项目ID');
            $table->smallInteger('attr_id')->comment('描述ID');
            $table->json('attr_value_id')->nullable()->comment('描述值');
            $table->dateTime('created_at');
            $table->dateTime('updated_at');
            // $table->primary(['project_id', 'attr_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('project_attr');
    }
}
