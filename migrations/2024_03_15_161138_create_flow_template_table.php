<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateFlowTemplateTable extends Migration
{
    protected $connection = 'tchip_redmine';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('flow_template')) {
            Schema::create('flow_template', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->char('flow_type', 16)->comment('模板类型');
                $table->string('name', 128)->comment('名称');
                $table->json('template')->nullable()->comment('模板内容');
                $table->tinyInteger('is_default')->default(0)->comment('是否默认');
                $table->dateTime('created_at');
                $table->dateTime('updated_at');
            });
        }

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('flow_template');
    }
}
