<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitQcTypeOaQcTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('oa_qc')) {
            Schema::table('oa_qc', function (Blueprint $table) {
                if (!Schema::hasColumn('oa_qc', 'qc_type')) {
                    $table->char('qc_type', 16)->comment('QC类型');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('', function (Blueprint $table) {
            //
        });
    }
}
