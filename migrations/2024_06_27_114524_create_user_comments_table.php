<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateUserCommentsTable extends Migration
{
    protected $connection = 'tchip_redmine';
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('user_comments')) {
            Schema::create('user_comments', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->integer('project_id')->comment('产品或项目id');
                $table->bigInteger('pid')->comment('评论id');
                $table->bigInteger('rpid')->comment('点评评论id');
                $table->bigInteger('rpcid')->comment('点评点评id');
                $table->integer('authorid', )->comment('点评人id');
                $table->string('author', 32)->comment('点评人名字');
                $table->string('rauthor', 32)->comment('回复人名字');
                $table->string('avatar')->comment('回复人头像');
                $table->integer('rauthorid')->comment('回复人id');
                $table->text('description')->comment('点评内容');
                $table->string('version_pre', 32)->nullable()->comment('旧版本');
                $table->string('version', 32)->nullable()->comment('版本');
                $table->longText('description_html')->nullable()->comment('内容html');
                $table->json('attachment_reversion')->nullable()->comment('改版记录的附件');
                $table->string('project_type')->comment('点评分类');
                $table->tinyInteger('progress_type')->nullable()->comment('进度分类');
                $table->tinyInteger('visible')->default(1)->comment('跟进可见类型1公开2通知人可见3指定人可见4指定人不可见');
                $table->json('visible_user')->nullable()->comment('可见用户或不可见用户');
                $table->json('workwx_user')->nullable()->comment('企业微信通知用户');
                $table->json('mail_user')->nullable()->comment('邮件通知用户');
                $table->text('notice_user')->nullable()->comment('通知对象');
                $table->json('rauthor_info')->nullable()->comment('被回复人的信息');
                $table->dateTime('created_at');
                $table->dateTime('updated_at');
                $table->dateTime('deleted_at')->nullable();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_comments');
    }
}
