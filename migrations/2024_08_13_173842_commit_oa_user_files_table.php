<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitOaUserFilesTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('oa_user_files')) {
            Schema::table('oa_user_files', function (Blueprint $table) {
                if (!Schema::hasColumn('oa_user_files', 'diploma_attachment_id')) {
                    $table->integer('diploma_attachment_id')->default(0)->comment('毕业证附件');
                }
                if (!Schema::hasColumn('oa_user_files', 'degree_attachment_id')) {
                    $table->integer('degree_attachment_id')->default(0)->comment('学位证附件');
                }
                if (!Schema::hasColumn('oa_user_files', 'identity_attachment_id')) {
                    $table->integer('identity_attachment_id')->default(0)->comment('身份证附件');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('oa_user_files', function (Blueprint $table) {
            //
        });
    }
}
