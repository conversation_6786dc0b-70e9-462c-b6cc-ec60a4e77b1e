<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateTestPlanLogTable extends Migration
{
    /**
     * 指定数据库连接
     */
    protected $connection = 'tchip_redmine';
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('test_plan_log')) {
            Schema::create('test_plan_log', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->integer('test_plan_id')->default(0)->comment('测试计划ID');
                $table->integer('relation_id')->default(0)->comment('字段关联表的id');
                $table->integer('created_by')->default(0)->comment('变更人ID');
                $table->string('change_type', 255)->default('')->comment('变更类型');
                $table->string('change_field', 255)->default('')->comment('变更属性');
                $table->text('old_value')->nullable()->comment('变更前的值');
                $table->text('new_value')->nullable()->comment('变更后的值');
                $table->dateTime('created_at')->nullable()->comment('创建时间');
                $table->dateTime('updated_at')->nullable()->comment('更新时间');
                $table->dateTime('deleted_at')->nullable()->comment('删除时间');
                //索引
                $table->index('test_plan_id');
            });
        }

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('test_plan_log', function (Blueprint $table) {
            //
        });
    }
}
