<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class AddConclusionToTestPlan extends Migration
{
 /**
     * 指定数据库连接
     */
    protected $connection = 'tchip_redmine';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('test_plan')) {
            Schema::table('test_plan', function (Blueprint $table) {
                // 添加结论字段
                if (!Schema::hasColumn('test_plan', 'conclusion')) {
                    $table->text('conclusion')->nullable()->comment('测试结论');
                }
                
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasTable('test_plan')) {
            Schema::table('test_plan', function (Blueprint $table) {
                // 回滚时删除添加的字段
                $columns = ['conclusion'];
                
                foreach ($columns as $column) {
                    if (Schema::hasColumn('test_plan', $column)) {
                        $table->dropColumn($column);
                    }
                }
            });
        }
    }
}
