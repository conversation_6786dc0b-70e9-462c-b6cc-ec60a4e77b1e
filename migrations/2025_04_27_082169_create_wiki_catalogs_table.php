<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateWikiCatalogsTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('wiki_catalogs', function (Blueprint $table) {
            $table->bigIncrements('catalog_id')->comment('目录ID');
            $table->unsignedBigInteger('space_id')->default(0)->comment('所属空间ID');
            $table->unsignedBigInteger('pid')->default(0)->comment('父目录ID');
            $table->string('path', 500)->default('/')->comment('完整路径，例：/1/5/9/');
            $table->string('name', 255)->comment('目录名称');
            $table->tinyInteger('is_public')->default(0)->comment('// 是否公开（20250508 pm 讨论暂定为 0默认组内共享|1公开共享非组员也可见|-1仅创建者私有可见）');
            $table->integer('sort_order')->default(0)->comment('排序');
            $table->unsignedBigInteger('created_by')->comment('创建人ID');
            $table->text('description')->nullable()->comment('描述');
            $table->timestamp('created_at')->useCurrent()->comment('创建时间');
            $table->timestamp('updated_at')->nullable()->comment('更新时间');
            $table->dateTime('deleted_at')->nullable()->comment('删除时间');

            // 索引
            $table->index('space_id');
            $table->index('pid');
            $table->index('path');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('', function (Blueprint $table) {
            //
        });
    }
}
