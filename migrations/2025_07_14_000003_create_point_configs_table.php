<?php

declare(strict_types=1);

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreatePointConfigsTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('point_configs', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('主键ID');
            $table->string('action_type', 50)->comment('行为类型标识');
            $table->string('action_name', 100)->comment('行为名称');
            $table->integer('points')->comment('基础积分值');
            $table->integer('max_daily_count')->nullable()->comment('每日最大获得次数(NULL表示无限制)');
            $table->integer('max_daily_points')->nullable()->comment('每日最大积分(NULL表示无限制)');
            $table->integer('first_time_bonus')->default(0)->comment('首次操作额外奖励');
            $table->json('consecutive_bonus')->nullable()->comment('连续操作奖励配置');
            $table->json('quality_multiplier')->nullable()->comment('质量倍数配置');
            $table->json('time_decay')->nullable()->comment('时间衰减配置');
            $table->tinyInteger('is_active')->default(1)->comment('是否启用');
            $table->string('category', 50)->default('general')->comment('分类：content, interaction, training, special');
            $table->integer('priority')->default(0)->comment('优先级(用于排序显示)');
            $table->text('description')->nullable()->comment('详细说明');
            $table->json('conditions')->nullable()->comment('触发条件配置');
            $table->timestamps();

            $table->unique('action_type', 'uk_action_type');
            $table->index('is_active', 'idx_is_active');
            $table->index('category', 'idx_category');
            $table->index('priority', 'idx_priority');
        });

        \Hyperf\DbConnection\Db::statement("ALTER TABLE `bi_point_configs` comment '积分系统-积分规则配置表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('point_configs');
    }
}