<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateFmsAclRulesTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('fms_acl_rules', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('主键ID');
            $table->enum('target_type', ['directory', 'file'])->comment('授权目标类型');
            $table->unsignedBigInteger('target_id')->comment('目录或文件ID');
            $table->unsignedBigInteger('subject_id')->comment('引用fms_subjects.id');
            $table->integer('permission_set')->comment('位掩码权限集细粒度权限集合（"view""upload""delete""share"）');
            $table->enum('effect', ['allow', 'deny'])->comment('允许/拒绝');
            $table->integer('priority')->default(0)->comment('规则优先级（越大越优先），用于不同种类规则冲突解决');
            $table->timestamps();

            // 索引
            $table->index(['target_type', 'target_id'], 'idx_fms_acl_rules_target');
            $table->index(['subject_id'], 'idx_fms_acl_rules_subject_id');
            $table->index(['effect'], 'idx_fms_acl_rules_effect');
            $table->index(['priority'], 'idx_fms_acl_rules_priority');

            // 外键约束
            $table->foreign('subject_id')->references('id')->on('fms_subjects')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('fms_acl_rules');
    }
}